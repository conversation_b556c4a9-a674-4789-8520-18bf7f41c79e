
## Usage

* This template uses tailwind css every tailwind class are prefixed with `tw-`, to help differentiate
  between tailwind classes and other classes  


During development add the following to head tag

```html
<link rel="stylesheet" href="./css/tailwind-runtime.css">
```
During production use

```html
<link rel="stylesheet" href="./css/tailwind-build.css">
```

To start Tailwind during development use
```html
npm run start:tailwind
```

To create a build file use
```html
npm run build:tailwind
```



For help contact [here](tally.so/r/woO0Kx). I'll get back to you in 24-48hrs during business days.